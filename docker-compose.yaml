version: "3.9"

services:
  teable:
    image: ghcr.io/teableio/teable:latest           # CE, no Redis needed
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - teable-data:/app/.assets:rw
    env_file:
      - .env
    environment:
      - NEXT_ENV_IMAGES_ALL_REMOTE=true
    depends_on:
      teable-db:
        condition: service_healthy
    networks:
      - teable
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health']
      start_period: 5s
      interval: 5s
      timeout: 3s
      retries: 3

  teable-db:
    image: postgres:15.4
    restart: always
    ports:
      - "42345:5432"
    volumes:
      - teable-db:/var/lib/postgresql/data:rw
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    healthcheck:
      test: ['CMD-SHELL', "sh -c 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}'"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - teable

  teable-storage:
    image: minio/minio:RELEASE.2024-02-17T01-15-57Z
    command: server /data --console-address ":9001"
    restart: always
    ports:
      - "9000:9000"     # S3-compatible API
      - "9001:9001"     # MinIO console
    environment:
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    volumes:
      - teable-storage:/data:rw
    networks:
      - teable

  createbuckets:
    image: minio/mc
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set teable-storage http://teable-storage:9000 ${MINIO_ACCESS_KEY} ${MINIO_SECRET_KEY};
      /usr/bin/mc mb teable-storage/${BACKEND_STORAGE_PUBLIC_BUCKET};
      /usr/bin/mc anonymous set public teable-storage/${BACKEND_STORAGE_PUBLIC_BUCKET};
      /usr/bin/mc mb teable-storage/${BACKEND_STORAGE_PRIVATE_BUCKET};
      exit 0;
      "
    depends_on:
      teable-storage:
        condition: service_started
    networks:
      - teable

networks:
  teable:
    name: teable-network

volumes:
  teable-db: {}
  teable-data: {}
  teable-storage: {}
