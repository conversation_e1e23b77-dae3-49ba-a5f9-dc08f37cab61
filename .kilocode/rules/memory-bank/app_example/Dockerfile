# Use Python 3.12 slim image
FROM python:3.12-slim



# Install system dependencies for pycairo, pygobject, and weasyprint
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    python3-dev \
    libcairo2-dev \
    libgirepository1.0-dev \
    libpango1.0-dev \
    libpangocairo-1.0-0 \
    libpango-1.0-0 \
    libcairo2 \
    libgdk-pixbuf2.0-0 \
    shared-mime-info \
    mime-support \
    fonts-thai-tlwg \
    fontconfig \
    wget \
    unzip \
    && fc-cache -f \
    && rm -rf /var/lib/apt/lists/*

# Download and install Sarabun font
RUN mkdir -p /usr/local/share/fonts/sarabun && \
    wget -q https://github.com/google/fonts/raw/main/ofl/sarabun/Sarabun-Regular.ttf -O /usr/local/share/fonts/sarabun/Sarabun-Regular.ttf && \
    wget -q https://github.com/google/fonts/raw/main/ofl/sarabun/Sarabun-Bold.ttf -O /usr/local/share/fonts/sarabun/Sarabun-Bold.ttf && \
    fc-cache -f

# Install uv
RUN pip install uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies using uv
RUN uv sync

# Copy application code and config
COPY app /app
COPY app/assets /app/assets
COPY .streamlit /root/.streamlit

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PORT=8080

# Expose the port Streamlit will run on
EXPOSE ${PORT}

# Run Streamlit using uv
WORKDIR /app
CMD ["uv", "run", "streamlit", "run", "main.py"]
