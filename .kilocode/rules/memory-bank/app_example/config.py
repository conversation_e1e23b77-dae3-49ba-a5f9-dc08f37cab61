from pathlib import Path
from typing import ClassVar, List
from pydantic import BaseModel, Field
from datetime import date
import yaml


class QuotationItem(BaseModel):
    description: str = Field(default="")
    qty: int = Field(default=0, ge=0)
    price: float = Field(default=0.0, ge=0.0)

    @property
    def total(self) -> float:
        return self.qty * self.price


class QuotationData(BaseModel):
    quotation_no: str
    date: str
    customer_name: str
    customer_address: str
    customer_tel: str
    items: List[dict]
    scope: str
    deliverables: str
    payment: str
    bank_account: str
    start_date: str | None = None
    end_date: str | None = None
    signature: str | None = None

    @property
    def total(self) -> float:
        return sum(QuotationItem(**item).total for item in self.items)


class Receipt(BaseModel):
    receipt_no: str
    date: str = Field(default_factory=lambda: str(date.today()))
    quotation_no: str
    customer_name: str
    customer_address: str
    customer_tel: str
    amount: float
    description: str
    signature: str | None = None

    @property
    def amount_in_thai(self) -> str:
        from utils import number_to_thai_text
        return number_to_thai_text(self.amount)


class Invoice(BaseModel):
    invoice_no: str
    date: str = Field(default_factory=lambda: str(date.today()))
    quotation_no: str
    customer_name: str
    customer_address: str
    customer_tel: str
    amount: float
    description: str
    signature: str | None = None

    @property
    def amount_in_thai(self) -> str:
        from utils import number_to_thai_text
        return number_to_thai_text(self.amount)


class CompanyConfig(BaseModel):
    company_name: str
    address_1: str
    address_2: str
    tel: str
    tax_id: str
    header_logo_path: str
    footer_logo_path: str
    bank_account: str 

    # Default configuration as a class variable
    DEFAULT_CONFIG: ClassVar[dict] = {
        "company_name": "Your Company Name",
        "address_1": "123 Business Street",
        "address_2": "City, Country 12345",
        "tel": "****** 567 8900",
        "tax_id": "TAX123456789",
        "header_logo_path": "assets/header_logo.png",
        "footer_logo_path": "assets/footer_logo.png",
        "bank_account": "ธนาคารกสิกรไทย เลขที่บัญชี xxx-x-xxxxx-x ชื่อบัญชี Your Company Name"
    }

    @classmethod
    def from_yaml(cls, path: str | Path) -> "CompanyConfig":
        """Load configuration from a YAML file"""
        with open(path, "r") as f:
            data = yaml.safe_load(f)
        return cls(**data)

    @classmethod
    def save_default_config(cls, path: str | Path = "config.yaml"):
        """Save default configuration to a YAML file if it doesn't exist"""
        config_path = Path(path)
        if not config_path.exists():
            with open(config_path, "w") as f:
                yaml.dump(cls.DEFAULT_CONFIG, f)
