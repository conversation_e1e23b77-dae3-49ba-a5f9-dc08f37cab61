import streamlit as st
from datetime import date
from pathlib import Path
import json
from typing import List, Dict, Any
import yaml
from config import CompanyConfig, QuotationItem, QuotationData, Receipt, Invoice
from utils import generate_pdf, number_to_thai_text, generate_receipt_pdf, generate_invoice_pdf
import base64

def initialize_session_state():
    """Initialize session state with default values"""
    # Initialize items list if not present
    if "items" not in st.session_state:
        st.session_state["items"] = [QuotationItem()]
    
    # Initialize next_id if not present
    if "next_id" not in st.session_state:
        st.session_state["next_id"] = 1
        
    # Initialize signature selection if not present
    if "signature" not in st.session_state:
        st.session_state["signature"] = None

    # Initialize logo selection if not present
    if "header_logo" not in st.session_state:
        st.session_state["header_logo"] = "chongko"
    if "footer_logo" not in st.session_state:
        st.session_state["footer_logo"] = "chongko"


def add_item():
    """Add a new item to the session state"""
    st.session_state["items"].append(QuotationItem())
    st.session_state["next_id"] += 1


def remove_item(index: int):
    """Remove an item from the session state"""
    if len(st.session_state["items"]) > 1:  # Ensure at least one item remains
        st.session_state["items"].pop(index)


def update_qty(index):
    item = st.session_state["items"][index]
    item.qty = st.session_state[f"qty_{index}"]


def update_price(index):
    item = st.session_state["items"][index]
    item.price = st.session_state[f"price_{index}"]


def save_state_to_json(filename: str, state_data: Dict[str, Any]):
    """Save current state to a JSON file"""
    save_dir = Path("saved_states")
    save_dir.mkdir(exist_ok=True)
    
    # Convert QuotationItems to dictionary
    items_data = []
    for item in state_data["items"]:
        items_data.append({
            "description": item.description,
            "qty": item.qty,
            "price": item.price
        })
    
    # Prepare data for saving
    save_data = {
        "quotation_no": state_data.get("quotation_no", ""),
        "date": state_data.get("date", str(date.today())),
        "customer_name": state_data.get("customer_name", ""),
        "customer_address": state_data.get("customer_address", ""),
        "customer_tel": state_data.get("customer_tel", ""),
        "items": items_data,
        "next_id": state_data.get("next_id", 1),
        "signature": state_data.get("signature", None),
        "header_logo": state_data.get("header_logo", "chongko"),
        "footer_logo": state_data.get("footer_logo", "chongko"),
        # Additional Information
        "start_date": state_data.get("start_date", None),
        "end_date": state_data.get("end_date", None),
        "scope": state_data.get("scope", ""),
        "deliverables": state_data.get("deliverables", ""),
        "payment": state_data.get("payment", ""),
        "bank_account": state_data.get("bank_account", "")
    }
    
    # Save to file with UTF-8 encoding
    with open(save_dir / f"{filename}.json", "w", encoding='utf-8') as f:
        json.dump(save_data, f, indent=2, default=str, ensure_ascii=False)


def load_state_from_json(filename: str) -> Dict[str, Any]:
    """Load state from a JSON file"""
    save_dir = Path("saved_states")
    file_path = save_dir / f"{filename}.json"
    
    # Open with UTF-8 encoding
    with open(file_path, "r", encoding='utf-8') as f:
        data = json.load(f)
    
    return data


def update_session_state_from_data(data: Dict[str, Any]):
    """Update session state from loaded data"""
    if "items" not in st.session_state:
        st.session_state["items"] = []
        
    # Convert items data back to QuotationItems
    items = []
    for item_data in data["items"]:
        item = QuotationItem()
        item.description = item_data["description"]
        item.qty = item_data["qty"]
        item.price = item_data["price"]
        items.append(item)
    
    # Update session state
    st.session_state["items"] = items
    st.session_state["next_id"] = data.get("next_id", 1)
    st.session_state["quotation_no"] = data.get("quotation_no", "")
    
    # Convert date strings to date objects
    try:
        st.session_state["date"] = date.fromisoformat(data.get("date", str(date.today())))
    except (ValueError, TypeError):
        st.session_state["date"] = date.today()
        
    st.session_state["customer_name"] = data.get("customer_name", "")
    st.session_state["customer_address"] = data.get("customer_address", "")
    st.session_state["customer_tel"] = data.get("customer_tel", "")
    st.session_state["scope"] = data.get("scope", "")
    st.session_state["deliverables"] = data.get("deliverables", "")
    st.session_state["payment"] = data.get("payment", "")
    st.session_state["bank_account"] = data.get("bank_account", "")
    st.session_state["header_logo"] = data.get("header_logo", "chongko")
    st.session_state["footer_logo"] = data.get("footer_logo", "chongko")
    
    # Convert start_date and end_date strings to date objects if they exist
    try:
        if data.get("start_date"):
            st.session_state["start_date"] = date.fromisoformat(data["start_date"])
        else:
            st.session_state["start_date"] = None
    except (ValueError, TypeError):
        st.session_state["start_date"] = None
        
    try:
        if data.get("end_date"):
            st.session_state["end_date"] = date.fromisoformat(data["end_date"])
        else:
            st.session_state["end_date"] = None
    except (ValueError, TypeError):
        st.session_state["end_date"] = None
    
    # Only set signature if it hasn't been set by a widget yet
    if "signature" not in st.session_state:
        st.session_state["signature"] = data.get("signature", "warm")


def get_saved_states() -> List[str]:
    """Get list of saved state filenames"""
    save_dir = Path("saved_states")
    save_dir.mkdir(exist_ok=True)
    return [f.stem for f in save_dir.glob("*.json")]


def save_receipt_to_json(receipt: Receipt):
    """Save receipt to a JSON file in the structure: saved_receipts/{quotation_no}/{receipt_no}.json"""
    base_dir = Path("saved_receipts")
    quotation_dir = base_dir / receipt.quotation_no
    quotation_dir.mkdir(parents=True, exist_ok=True)
    
    save_data = receipt.dict()
    
    # Save to file with UTF-8 encoding
    with open(quotation_dir / f"{receipt.receipt_no}.json", "w", encoding='utf-8') as f:
        json.dump(save_data, f, indent=2, default=str, ensure_ascii=False)


def get_saved_receipts(quotation_no: str = None) -> List[str]:
    """Get list of saved receipt filenames.
    If quotation_no is provided, returns receipts for that quotation only.
    Otherwise returns all receipts."""
    base_dir = Path("saved_receipts")
    base_dir.mkdir(exist_ok=True)
    
    if quotation_no:
        quotation_dir = base_dir / quotation_no
        if not quotation_dir.exists():
            return []
        return [f.stem for f in quotation_dir.glob("*.json")]
    
    # If no quotation_no, get all receipts from all quotations
    receipts = []
    for quotation_dir in base_dir.glob("*"):
        if quotation_dir.is_dir():
            for receipt_file in quotation_dir.glob("*.json"):
                receipts.append(f"{quotation_dir.name}/{receipt_file.stem}")
    return receipts


def load_receipt_from_json(receipt_path: str) -> Receipt:
    """Load receipt from a JSON file.
    receipt_path can be either 'quotation_no/receipt_no' or just 'receipt_no'"""
    base_dir = Path("saved_receipts")
    
    if "/" in receipt_path:
        # If path contains quotation number
        quotation_no, receipt_no = receipt_path.split("/")
        file_path = base_dir / quotation_no / f"{receipt_no}.json"
    else:
        # For backward compatibility, search in all quotation folders
        receipt_no = receipt_path
        for quotation_dir in base_dir.glob("*"):
            if quotation_dir.is_dir():
                potential_path = quotation_dir / f"{receipt_no}.json"
                if potential_path.exists():
                    file_path = potential_path
                    break
        else:
            raise FileNotFoundError(f"Receipt {receipt_no} not found")
    
    with open(file_path, "r", encoding='utf-8') as f:
        data = json.load(f)
    
    return Receipt(**data)


def get_next_installment_number(quotation_no: str) -> int:
    """Get the next installment number for a quotation based on existing receipts"""
    base_dir = Path("saved_receipts")
    quotation_dir = base_dir / quotation_no
    if not quotation_dir.exists():
        return 1
    return len(list(quotation_dir.glob("*.json"))) + 1


def generate_receipt_preview(receipt: Receipt, company_config: CompanyConfig) -> str:
    """Generate a preview of the receipt in HTML format"""
    html = f"""
    <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: auto;">
        <div style="text-align: right; margin-bottom: 20px;">
            <h2>ใบเสร็จรับเงิน / RECEIPT</h2>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div>
                <p><strong>เลขที่ / No:</strong> {receipt.receipt_no}</p>
                <p><strong>วันที่ / Date:</strong> {receipt.date}</p>
            </div>
            <div>
                <p><strong>{company_config.company_name}</strong></p>
                <p>{company_config.address_1}</p>
                <p>{company_config.address_2}</p>
                <p>Tel: {company_config.tel}</p>
                <p>Tax ID: {company_config.tax_id}</p>
            </div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <p><strong>ได้รับเงินจาก / Received from:</strong></p>
            <p>{receipt.customer_name}</p>
            <p>{receipt.customer_address}</p>
            <p>Tel: {receipt.customer_tel}</p>
        </div>
        
        <div style="margin-bottom: 20px;">
            <p><strong>รายการ / Description:</strong></p>
            <p>{receipt.description}</p>
        </div>
        
        <div style="text-align: right; margin-bottom: 20px;">
            <p><strong>จำนวนเงิน / Amount:</strong> ฿{receipt.amount:,.2f}</p>
            <p><strong>({number_to_thai_text(receipt.amount)})</strong></p>
        </div>
        
        <div style="margin-top: 40px; text-align: center;">
            <p>ขอบคุณที่ใช้บริการ / Thank you for your business</p>
        </div>
    </div>
    """
    return html


def migrate_existing_receipts():
    """Migrate existing receipts to the new folder structure"""
    base_dir = Path("saved_receipts")
    if not base_dir.exists():
        return
        
    # Find all json files in the root saved_receipts directory
    for receipt_file in base_dir.glob("*.json"):
        try:
            # Load the receipt
            with open(receipt_file, "r", encoding='utf-8') as f:
                data = json.load(f)
                receipt = Receipt(**data)
            
            # Create quotation directory
            quotation_dir = base_dir / receipt.quotation_no
            quotation_dir.mkdir(parents=True, exist_ok=True)
            
            # Move file to new location
            new_path = quotation_dir / receipt_file.name
            if not new_path.exists():  # Don't overwrite if already exists
                receipt_file.rename(new_path)
        except Exception as e:
            print(f"Error migrating receipt {receipt_file}: {e}")


def check_password():
    """Returns `True` if the user had the correct password."""

    def password_entered():
        """Checks whether a password entered by the user is correct."""
        with open("config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        if str(st.session_state["password"]) == str(config['pass']):
            st.session_state["password_correct"] = True
            del st.session_state["password"]  # Don't store the password
        else:
            st.session_state["password_correct"] = False

    if "password_correct" not in st.session_state:
        # First run, show input for password
        st.text_input(
            "Please enter the password", 
            type="password", 
            on_change=password_entered, 
            key="password"
        )
        return False
    
    elif not st.session_state["password_correct"]:
        # Password incorrect, show input + error
        st.text_input(
            "Please enter the password", 
            type="password", 
            on_change=password_entered, 
            key="password"
        )
        st.error("😕 Password incorrect")
        return False
    else:
        # Password correct
        return True


def save_invoice_to_json(invoice: Invoice):
    """Save invoice to a JSON file in the structure: saved_invoices/{quotation_no}/{invoice_no}.json"""
    base_dir = Path("saved_invoices")
    quotation_dir = base_dir / invoice.quotation_no
    quotation_dir.mkdir(parents=True, exist_ok=True)
    
    save_data = invoice.dict()
    
    # Save to file with UTF-8 encoding
    with open(quotation_dir / f"{invoice.invoice_no}.json", "w", encoding='utf-8') as f:
        json.dump(save_data, f, indent=2, default=str, ensure_ascii=False)


def get_saved_invoices(quotation_no: str = None) -> List[str]:
    """Get list of saved invoice filenames.
    If quotation_no is provided, returns invoices for that quotation only.
    Otherwise returns all invoices."""
    base_dir = Path("saved_invoices")
    base_dir.mkdir(exist_ok=True)
    
    if quotation_no:
        quotation_dir = base_dir / quotation_no
        if not quotation_dir.exists():
            return []
        return [f.stem for f in quotation_dir.glob("*.json")]
    
    # If no quotation_no, get all invoices from all quotations
    invoices = []
    for quotation_dir in base_dir.glob("*"):
        if quotation_dir.is_dir():
            for invoice_file in quotation_dir.glob("*.json"):
                invoices.append(f"{quotation_dir.name}/{invoice_file.stem}")
    return invoices


def load_invoice_from_json(invoice_path: str) -> Invoice:
    """Load invoice from a JSON file.
    invoice_path can be either 'quotation_no/invoice_no' or just 'invoice_no'"""
    base_dir = Path("saved_invoices")
    
    if "/" in invoice_path:
        # If path contains quotation number
        quotation_no, invoice_no = invoice_path.split("/")
        file_path = base_dir / quotation_no / f"{invoice_no}.json"
    else:
        # For backward compatibility, search in all quotation folders
        invoice_no = invoice_path
        for quotation_dir in base_dir.glob("*"):
            if quotation_dir.is_dir():
                potential_path = quotation_dir / f"{invoice_no}.json"
                if potential_path.exists():
                    file_path = potential_path
                    break
        else:
            raise FileNotFoundError(f"Invoice {invoice_no} not found")
    
    with open(file_path, "r", encoding='utf-8') as f:
        data = json.load(f)
    
    return Invoice(**data)


def get_next_invoice_number(quotation_no: str) -> int:
    """Get the next invoice number for a quotation based on existing invoices"""
    base_dir = Path("saved_invoices")
    quotation_dir = base_dir / quotation_no
    if not quotation_dir.exists():
        return 1
    return len(list(quotation_dir.glob("*.json"))) + 1


def main():
    if not check_password():
        st.stop()  # Do not continue if check_password is not True
        
    # Migrate any existing receipts to new structure
    migrate_existing_receipts()
    
    # Initialize session state first
    initialize_session_state()
    
    # Load company configuration
    config_path = Path("config.yaml")
    if not config_path.exists():
        CompanyConfig.save_default_config(config_path)
    company_config = CompanyConfig.from_yaml(config_path)
    
    st.title("Document Generator")
    
    # Create tabs for quotation and receipt
    tab1, tab2, tab3 = st.tabs(["Quotation", "Receipt", "Invoice"])
    
    with tab1:
        # Ensure config file exists
        CompanyConfig.save_default_config()
        
        # Add save/load functionality in a new row
        col_load, col_save = st.columns(2)
        
        with col_load:
            saved_states = get_saved_states()
            if saved_states:
                selected_state = st.selectbox("Select saved quotation", saved_states)
                if st.button("Load"):
                    form_data = load_state_from_json(selected_state)
                    update_session_state_from_data(form_data)
                    st.rerun()
        
        with col_save:
            if st.button("Save Current State"):
                if not st.session_state.get("quotation_no"):
                    st.error("Please enter a quotation number before saving")
                else:
                    # Create state data with form variables
                    state_data = {
                        "quotation_no": st.session_state["quotation_no"],
                        "date": st.session_state["date"].isoformat() if isinstance(st.session_state["date"], date) else str(date.today()),
                        "customer_name": st.session_state["customer_name"],
                        "customer_address": st.session_state["customer_address"],
                        "customer_tel": st.session_state["customer_tel"],
                        "items": st.session_state["items"],
                        "next_id": st.session_state["next_id"],
                        "signature": st.session_state["signature"],
                        "header_logo": st.session_state["header_logo"],
                        "footer_logo": st.session_state["footer_logo"],
                        "scope": st.session_state["scope"],
                        "deliverables": st.session_state["deliverables"],
                        "payment": st.session_state["payment"],
                        "bank_account": st.session_state["bank_account"],
                        "start_date": st.session_state["start_date"].isoformat() if st.session_state.get("start_date") else None,
                        "end_date": st.session_state["end_date"].isoformat() if st.session_state.get("end_date") else None
                    }
                    save_state_to_json(st.session_state["quotation_no"], state_data)
                    st.success(f"Saved state for quotation {st.session_state['quotation_no']}")
        
        # Create two columns for the form
        col1, col2 = st.columns(2)

        with col1:
            st.text_input("Quotation Number", key="quotation_no", value=st.session_state.get("quotation_no", ""))
            st.date_input("Date", value=st.session_state.get("date", date.today()), key="date")
            st.text_input("Customer Name", key="customer_name", value=st.session_state.get("customer_name", ""))
            st.text_area("Customer Address", key="customer_address", value=st.session_state.get("customer_address", ""))
            st.text_input("Customer Telephone", key="customer_tel", value=st.session_state.get("customer_tel", ""))

        with col2:
            st.subheader("Company Information")
            st.text(f"Company: {company_config.company_name}")
            st.text(f"Address: {company_config.address_1}")
            st.text(f"         {company_config.address_2}")
            st.text(f"Tel: {company_config.tel}")
            st.text(f"Tax ID: {company_config.tax_id}")

        st.subheader("Items")
        
        # Display items
        items_to_remove = []  # Track items to remove
        for index, item in enumerate(st.session_state["items"]):
            col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 0.5])
            
            with col1:
                description = st.text_input(
                    "Description", 
                    key=f"desc_{index}", 
                    value=item.description
                )
                item.description = description
                
            with col2:
                st.number_input(
                    "Quantity", 
                    key=f"qty_{index}", 
                    min_value=0,
                    step=1,
                    value=item.qty,
                    on_change=update_qty,
                    args=(index,)
                )
                
            with col3:
                st.number_input(
                    "Price", 
                    key=f"price_{index}", 
                    min_value=0.0, 
                    step=100.0,
                    value=item.price,
                    on_change=update_price,
                    args=(index,)
                )
                
            with col4:
                st.text(f"Total: {item.total:.2f}")
                
            with col5:
                if len(st.session_state["items"]) > 1:
                    if st.button("🗑", key=f"del_{index}"):
                        items_to_remove.append(index)

        # Remove items after iteration
        for index in reversed(items_to_remove):
            remove_item(index)
            st.rerun()

        if st.button("Add Item"):
            add_item()
            st.rerun()

        # Calculate total
        total = sum(item.total for item in st.session_state["items"])
        st.subheader(f"Total Amount: {total:.2f}")
        st.text(f"({number_to_thai_text(total)})")
        
        # Project Timeline
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("Project Start Date", key="start_date", value=st.session_state.get("start_date"))
        with col2:
            end_date = st.date_input("Project End Date", key="end_date", value=st.session_state.get("end_date"))
        
        # Display project duration if both dates are selected
        if start_date and end_date:
            if end_date >= start_date:
                delta = end_date - start_date
                total_days = delta.days
                months = total_days // 30
                remaining_days = total_days % 30
                weeks = remaining_days // 7
                days = remaining_days % 7
                
                duration_text = "Project Duration: "
                if months > 0:
                    duration_text += f"{months} month{'s' if months > 1 else ''} "
                if weeks > 0:
                    duration_text += f"{weeks} week{'s' if weeks > 1 else ''} "
                if days > 0:
                    duration_text += f"{days} day{'s' if days > 1 else ''}"
                
                st.info(duration_text.strip())
            else:
                st.error("End date must be after start date")
        
        # Additional fields
        st.subheader("Additional Information")
        scope = st.text_area("Scope of Work", key="scope", value=st.session_state.get("scope", ""))
        deliverables = st.text_area("Deliverables", key="deliverables", value=st.session_state.get("deliverables", ""))
        
        # Calculate total for payment section
        total = sum(item.total for item in st.session_state["items"])
        
        # Payment and Bank Account sections
        payment = st.text_area(
            "Payment (การชำระเงิน)", 
            key="payment", 
            value=st.session_state.get("payment", f"งวดที่ 1 ชำระ 50% เป็นเงิน {total/2:,.2f} บาท เมื่อสั่งซื้อ และ 50% ที่เหลือเมื่องานเสร็จ")
        )
        bank_account = st.text_area(
            "Bank Account (โอนเงินมาที่)", 
            key="bank_account", 
            value=st.session_state.get("bank_account", company_config.bank_account)
        )

        # Select signature
        col1, col2 = st.columns([1, 2])
        with col1:
            st.radio(
                "Select Signature",
                options=["warm", "may"],
                key="signature",
                horizontal=True,
                index=0 if st.session_state.get("signature") == "warm" else 1
            )
        
        # Add logo selection
        col1, col2 = st.columns(2)
        with col1:
            st.radio(
                "Select Header Logo",
                options=["chongko", "mager"],
                key="header_logo",
                horizontal=True,
                index=0 if st.session_state.get("header_logo", "chongko") == "chongko" else 1
            )
        with col2:
            st.radio(
                "Select Footer Logo",
                options=["chongko", "mager"],
                key="footer_logo",
                horizontal=True,
                index=0 if st.session_state.get("footer_logo", "chongko") == "chongko" else 1
            )
        
        # Generate PDF button
        if st.button("Generate PDF"):
            try:
                # Validate all required fields
                if not all([st.session_state.get("quotation_no"), st.session_state.get("customer_name"), st.session_state.get("customer_address"), st.session_state.get("customer_tel")]):
                    st.error("Please fill in all required fields")
                    return

                # Set logo paths based on selection
                logo_paths = {
                    "chongko": "assets/cc_logo.png",
                    "mager": "assets/mager_logo.jpg"
                }
                
                company_config.header_logo_path = logo_paths[st.session_state["header_logo"]]
                company_config.footer_logo_path = logo_paths[st.session_state["footer_logo"]]

                # Convert QuotationItem instances to dictionaries
                items_data = [
                    {
                        "description": item.description,
                        "qty": item.qty,
                        "price": item.price
                    }
                    for item in st.session_state["items"]
                ]

                # Create QuotationData instance
                quotation_data = QuotationData(
                    quotation_no=st.session_state["quotation_no"],
                    date=st.session_state["date"].strftime("%Y-%m-%d"),
                    customer_name=st.session_state["customer_name"],
                    customer_address=st.session_state["customer_address"],
                    customer_tel=st.session_state["customer_tel"],
                    items=[item.dict() for item in st.session_state["items"]],
                    scope=st.session_state["scope"],
                    deliverables=st.session_state["deliverables"],
                    payment=st.session_state["payment"],
                    bank_account=st.session_state["bank_account"],
                    start_date=st.session_state["start_date"].strftime("%Y-%m-%d") if st.session_state.get("start_date") else None,
                    end_date=st.session_state["end_date"].strftime("%Y-%m-%d") if st.session_state.get("end_date") else None,
                    signature=st.session_state["signature"],
                    header_logo=st.session_state["header_logo"],
                    footer_logo=st.session_state["footer_logo"]
                )
                
                # Generate PDF and get bytes
                pdf_bytes = generate_pdf(quotation_data, company_config)
                
                # Save PDF file
                pdf_dir = Path("saved_quotations") / st.session_state["quotation_no"] / "pdfs"
                pdf_dir.mkdir(parents=True, exist_ok=True)
                pdf_path = pdf_dir / f"quotation_{st.session_state['quotation_no']}.pdf"
                pdf_path.write_bytes(pdf_bytes)
                
                # Display PDF in the app
                st.success("PDF generated and saved successfully!")
                st.download_button(
                    label="Download PDF",
                    data=pdf_bytes,
                    file_name=f"quotation_{st.session_state['quotation_no']}.pdf",
                    mime="application/pdf"
                )
                
                # Display PDF directly in the app
                base64_pdf = base64.b64encode(pdf_bytes).decode('utf-8')
                pdf_display = f'<iframe src="data:application/pdf;base64,{base64_pdf}" width="100%" height="800" type="application/pdf"></iframe>'
                st.markdown(pdf_display, unsafe_allow_html=True)
                
            except Exception as e:
                st.error(f"Error generating PDF: {str(e)}")

    with tab2:
        st.header("Receipt Generation")
        
        # Load existing quotation
        saved_states = get_saved_states()
        if not saved_states:
            st.warning("No saved quotations found. Please create a quotation first.")
            return
            
        selected_quotation = st.selectbox(
            "Select Quotation",
            saved_states,
            key="receipt_quotation_select"
        )
        
        if selected_quotation:
            quotation_data = load_state_from_json(selected_quotation)
            total = sum(item['price']*item['qty'] for item in quotation_data['items'])
            
            # Display quotation details
            st.subheader("Quotation Details")
            st.write(f"Customer: {quotation_data['customer_name']}")
            st.write(f"Total Amount: ฿{total:,.2f}")
            
            # Get next installment number
            next_installment = get_next_installment_number(selected_quotation)
            
            # Receipt form
            with st.form("receipt_form"):
                st.subheader("Receipt Information")
                receipt_no = st.text_input("Receipt Number")
                receipt_date = st.date_input("Receipt Date", date.today())
                receipt_amount = st.number_input("Receipt Amount", 
                                            min_value=0.0, 
                                            max_value=float(quotation_data['items'][0]['price']),
                                            step=1000.0)
                receipt_description = st.text_area("Description", 
                                                value=f"งวดที่ {next_installment} จากใบเสนอราคาเลขที่ {selected_quotation}")
                
                # Signature selection
                st.subheader("Signature")
                signature_choice = st.radio(
                    "Select Signature",
                    options=["warm", "may"],
                    horizontal=True,
                    key="receipt_signature"
                )
                
                # Logo selection
                col1, col2 = st.columns(2)
                with col1:
                    st.radio(
                        "Select Header Logo",
                        options=["chongko", "mager"],
                        key="receipt_header_logo",
                        horizontal=True,
                        index=0 if st.session_state.get("receipt_header_logo", "chongko") == "chongko" else 1
                    )
                with col2:
                    st.radio(
                        "Select Footer Logo",
                        options=["chongko", "mager"],
                        key="receipt_footer_logo",
                        horizontal=True,
                        index=0 if st.session_state.get("receipt_footer_logo", "chongko") == "chongko" else 1
                    )

                # Submit button
                submitted = st.form_submit_button("Generate Receipt")
                
            # Handle form submission outside the form
            if submitted:
                if not receipt_no:
                    st.error("Please enter a receipt number")
                else:
                    try:
                        receipt = Receipt(
                            receipt_no=receipt_no,
                            date=str(receipt_date),
                            quotation_no=selected_quotation,
                            customer_name=quotation_data['customer_name'],
                            customer_address=quotation_data['customer_address'],
                            customer_tel=quotation_data['customer_tel'],
                            amount=receipt_amount,
                            description=receipt_description,
                            signature=signature_choice
                        )
                        
                        # Set logo paths based on selection
                        logo_paths = {
                            "chongko": "assets/cc_logo.png",
                            "mager": "assets/mager_logo.jpg"
                        }
                        
                        company_config.header_logo_path = logo_paths[st.session_state["receipt_header_logo"]]
                        company_config.footer_logo_path = logo_paths[st.session_state["receipt_footer_logo"]]

                        # Generate PDF
                        pdf_bytes = generate_receipt_pdf(receipt, company_config)
                        
                        # Save receipt to JSON
                        save_receipt_to_json(receipt)
                        
                        # Save PDF file
                        pdf_dir = Path("saved_receipts") / selected_quotation / "pdfs"
                        pdf_dir.mkdir(parents=True, exist_ok=True)
                        pdf_path = pdf_dir / f"{receipt_no}.pdf"
                        pdf_path.write_bytes(pdf_bytes)
                        
                        st.success(f"Receipt {receipt_no} generated and saved successfully!")
                        
                        # Display the PDF
                        base64_pdf = base64.b64encode(pdf_bytes).decode('utf-8')
                        pdf_display = f'<iframe src="data:application/pdf;base64,{base64_pdf}" width="100%" height="800" type="application/pdf"></iframe>'
                        st.markdown(pdf_display, unsafe_allow_html=True)
                        
                        # Add download button
                        st.download_button(
                            label="Download Receipt PDF",
                            data=pdf_bytes,
                            file_name=f"receipt_{receipt_no}.pdf",
                            mime="application/pdf"
                        )
                        
                    except Exception as e:
                        st.error(f"Error generating receipt: {str(e)}")
            
            # Display existing receipts for this quotation
            st.subheader("Existing Receipts")
            existing_receipts = [
                load_receipt_from_json(f"{selected_quotation}/{r}") 
                for r in get_saved_receipts(selected_quotation)
            ]
            
            if existing_receipts:
                total_received = 0.0
                for i, receipt in enumerate(sorted(existing_receipts, key=lambda x: x.date), 1):
                    total_received += receipt.amount
                    col1, col2, col3 = st.columns([2, 2, 1])
                    with col1:
                        st.write(f"Receipt {receipt.receipt_no} (งวดที่ {i})")
                    with col2:
                        st.write(f"฿{receipt.amount:,.2f}")
                    with col3:
                        st.write(f"{receipt.date}")
                
                st.write("---")
                st.write(f"Total Amount Received: ฿{total_received:,.2f}")
                remaining = total - total_received
                st.write(f"Remaining Amount: ฿{remaining:,.2f}")
            else:
                st.info("No receipts generated for this quotation yet")

    with tab3:
        st.header("Invoice Generation")
        
        # Load existing quotation
        saved_states = get_saved_states()
        if not saved_states:
            st.warning("No saved quotations found. Please create a quotation first.")
            return
            
        selected_quotation = st.selectbox(
            "Select Quotation",
            saved_states,
            key="invoice_quotation_select"
        )
        
        if selected_quotation:
            quotation_data = load_state_from_json(selected_quotation)
            total = sum(item['price']*item['qty'] for item in quotation_data['items'])
            
            # Display quotation details
            st.subheader("Quotation Details")
            st.write(f"Customer: {quotation_data['customer_name']}")
            st.write(f"Total Amount: ฿{total:,.2f}")
            
            # Get next installment number
            next_installment = get_next_invoice_number(selected_quotation)
            
            # Invoice form
            with st.form("invoice_form"):
                st.subheader("Invoice Information")
                invoice_no = st.text_input("Invoice Number")
                invoice_date = st.date_input("Invoice Date", date.today())
                invoice_amount = st.number_input("Invoice Amount", 
                                            min_value=0.0, 
                                            max_value=float(total),
                                            step=1000.0)
                invoice_description = st.text_area("Description", 
                                                value=f"งวดที่ {next_installment} จากใบเสนอราคาเลขที่ {selected_quotation}")
                
                # Signature selection
                st.subheader("Signature")
                signature_choice = st.radio(
                    "Select Signature",
                    options=["warm", "may"],
                    horizontal=True,
                    key="invoice_signature"
                )
                
                # Logo selection
                col1, col2 = st.columns(2)
                with col1:
                    st.radio(
                        "Select Header Logo",
                        options=["chongko", "mager"],
                        key="invoice_header_logo",
                        horizontal=True,
                        index=0 if st.session_state.get("invoice_header_logo", "chongko") == "chongko" else 1
                    )
                with col2:
                    st.radio(
                        "Select Footer Logo",
                        options=["chongko", "mager"],
                        key="invoice_footer_logo",
                        horizontal=True,
                        index=0 if st.session_state.get("invoice_footer_logo", "chongko") == "chongko" else 1
                    )

                # Submit button
                submitted = st.form_submit_button("Generate Invoice")
                
            # Handle form submission outside the form
            if submitted:
                if not invoice_no:
                    st.error("Please enter an invoice number")
                else:
                    try:
                        invoice = Invoice(
                            invoice_no=invoice_no,
                            date=str(invoice_date),
                            quotation_no=selected_quotation,
                            customer_name=quotation_data['customer_name'],
                            customer_address=quotation_data['customer_address'],
                            customer_tel=quotation_data['customer_tel'],
                            amount=invoice_amount,
                            description=invoice_description,
                            signature=signature_choice
                        )
                        
                        # Set logo paths based on selection
                        logo_paths = {
                            "chongko": "assets/cc_logo.png",
                            "mager": "assets/mager_logo.jpg"
                        }
                        
                        company_config.header_logo_path = logo_paths[st.session_state["invoice_header_logo"]]
                        company_config.footer_logo_path = logo_paths[st.session_state["invoice_footer_logo"]]

                        # Generate PDF
                        pdf_bytes = generate_invoice_pdf(invoice, company_config)
                        
                        # Save invoice to JSON
                        save_invoice_to_json(invoice)
                        
                        # Save PDF file
                        pdf_dir = Path("saved_invoices") / selected_quotation / "pdfs"
                        pdf_dir.mkdir(parents=True, exist_ok=True)
                        pdf_path = pdf_dir / f"{invoice_no}.pdf"
                        pdf_path.write_bytes(pdf_bytes)
                        
                        st.success(f"Invoice {invoice_no} generated and saved successfully!")
                        
                        # Display the PDF
                        base64_pdf = base64.b64encode(pdf_bytes).decode('utf-8')
                        pdf_display = f'<iframe src="data:application/pdf;base64,{base64_pdf}" width="100%" height="800" type="application/pdf"></iframe>'
                        st.markdown(pdf_display, unsafe_allow_html=True)
                        
                        # Add download button
                        st.download_button(
                            label="Download Invoice PDF",
                            data=pdf_bytes,
                            file_name=f"invoice_{invoice_no}.pdf",
                            mime="application/pdf"
                        )
                        
                    except Exception as e:
                        st.error(f"Error generating invoice: {str(e)}")
            
            # Display existing invoices for this quotation
            st.subheader("Existing Invoices")
            existing_invoices = [
                load_invoice_from_json(f"{selected_quotation}/{r}") 
                for r in get_saved_invoices(selected_quotation)
            ]
            
            if existing_invoices:
                total_invoiced = 0.0
                for i, invoice in enumerate(sorted(existing_invoices, key=lambda x: x.date), 1):
                    total_invoiced += invoice.amount
                    col1, col2, col3 = st.columns([2, 2, 1])
                    with col1:
                        st.write(f"Invoice {invoice.invoice_no} (งวดที่ {i})")
                    with col2:
                        st.write(f"฿{invoice.amount:,.2f}")
                    with col3:
                        st.write(f"{invoice.date}")
                
                st.write("---")
                st.write(f"Total Amount Invoiced: ฿{total_invoiced:,.2f}")
                remaining = total - total_invoiced
                st.write(f"Remaining Amount: ฿{remaining:,.2f}")
            else:
                st.info("No invoices generated for this quotation yet")

if __name__ == "__main__":
    main()
