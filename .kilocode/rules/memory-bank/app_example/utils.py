from pathlib import Path
import os
from io import BytesIO
from config import QuotationData, CompanyConfig, Receipt, Invoice
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
from jinja2 import Template
import base64
from datetime import datetime, date

def number_to_thai_text(number):
    """Convert a number to Thai text reading."""
    # Thai number words
    thai_numbers = ["", "หนึ่ง", "สอง", "สาม", "สี่", "ห้า", "หก", "เจ็ด", "แปด", "เก้า"]
    thai_units = ["", "สิบ", "ร้อย", "พัน", "หมื่น", "แสน", "ล้าน"]
    
    def convert_group(n):
        """Convert numbers from 1 to 999999."""
        if n == 0:
            return ""
            
        result = []
        unit_idx = 0
        
        while n > 0:
            digit = n % 10
            if digit > 0:
                # Special case for 1 in tens position
                if unit_idx == 1 and digit == 1:
                    result.append(thai_units[unit_idx])
                # Special case for 2 in tens position
                elif unit_idx == 1 and digit == 2:
                    result.append("ยี่" + thai_units[unit_idx])
                else:
                    result.append(thai_numbers[digit] + thai_units[unit_idx])
            n //= 10
            unit_idx += 1
            
        return "".join(reversed(result))
    
    # Handle zero
    if number == 0:
        return "ศูนย์บาทถ้วน"
    
    # Split into integer and decimal parts
    number = float(number)
    integer_part = int(number)
    decimal_part = int(round((number - integer_part) * 100))
    
    # Convert integer part
    result = []
    if integer_part > 0:
        millions = integer_part // 1000000
        remainder = integer_part % 1000000
        
        if millions > 0:
            result.append(convert_group(millions) + "ล้าน")
        if remainder > 0:
            result.append(convert_group(remainder))
            
    text = "".join(result) + "บาท"
    
    # Add decimal part if exists
    if decimal_part > 0:
        text += convert_group(decimal_part) + "สตางค์"
    else:
        text += "ถ้วน"
        
    return text

def format_date_thai_eng(date_str):
    """Convert date to Thai-English format."""
    # Thai month names
    thai_months = {
        1: "มกราคม", 2: "กุมภาพันธ์", 3: "มีนาคม", 4: "เมษายน",
        5: "พฤษภาคม", 6: "มิถุนายน", 7: "กรกฎาคม", 8: "สิงหาคม",
        9: "กันยายน", 10: "ตุลาคม", 11: "พฤศจิกายน", 12: "ธันวาคม"
    }
    
    # Parse the input date
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    
    # Format Thai date (Buddhist era)
    thai_year = date_obj.year + 543
    thai_month = thai_months[date_obj.month]
    thai_date = f"{date_obj.day} {thai_month} {thai_year}"
    
    # Format English date
    eng_date = date_obj.strftime("%d-%m-%Y")
    
    return f"{thai_date} / {eng_date}"

def generate_pdf(data: QuotationData, config: CompanyConfig) -> bytes:
    """Generate a PDF quotation using WeasyPrint with proper Thai text support."""
    # Configure Fonts
    font_config = FontConfiguration()
    
    # Signature path
    signature_dict = {
        "warm": "assets/warm_sign.jpg",
        "may": "assets/may_sign.png"
    }
    
    # Function to get image data URL
    def get_image_data_url(image_path):
        if not image_path:
            return None
        try:
            if not Path(image_path).exists():
                return None
            with open(image_path, 'rb') as img_file:
                encoded = base64.b64encode(img_file.read()).decode()
                mime_type = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'
                return f'data:{mime_type};base64,{encoded}'
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            return None

    # Get image data URLs
    header_logo = get_image_data_url(config.header_logo_path)
    footer_logo = get_image_data_url(config.footer_logo_path)
    
    # Get signature
    signature_image = None
    if data.signature and data.signature in signature_dict:
        signature_image = get_image_data_url(signature_dict[data.signature])
        
    # Create HTML template
    template_str = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            @font-face {
                font-family: 'Sarabun';
                src: url('/usr/local/share/fonts/sarabun/Sarabun-Regular.ttf') format('truetype');
                font-weight: normal;
            }
            @font-face {
                font-family: 'Sarabun';
                src: url('/usr/local/share/fonts/sarabun/Sarabun-Bold.ttf') format('truetype');
                font-weight: bold;
            }
            @page {
                size: A4;
                margin: 0.3cm 2cm 0.3cm 2.5cm;
            }
            body {
                font-family: 'Sarabun', sans-serif;
                font-size: 14px;
                line-height: 1.3;
                margin: 0;
                padding-top: 0cm;  
                padding-bottom: 0cm;  
            }
            .header {
                width: 100%;
                margin-bottom: 0.5cm;
            }
            .footer {
                position: running(footer);
                width: 100%;
                margin-top: 0.5cm;  
            }
            .header-logo {
                text-align: center;
                margin-top: 5px;
            }
            .header-logo img {
                height: 80px;
                max-width: 200px;
                object-fit: contain;
            }
            .footer-logo {
                text-align: center;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                margin-bottom: 5px;
            }
            .footer-logo img {
                height: 80px;
                max-width: 200px;
                object-fit: contain;
            }
            .quotation-title {
                font-size: 20px;
                font-weight: bold;
                text-align: right;
                margin: 10px 0;
                padding: 5px;
                display: block;
            }
            .info-container {
                display: flex;
                justify-content: space-between;
                margin-top: 10px;
                margin-bottom: 20px;
            }
            .company-info {
                flex: 1;
            }
            .quotation-info {
                text-align: right;
                margin-left: 20px;
            }
            .customer-info {
                margin: 20px 0;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 1em 0;
            }
            th, td {
                border: 1px solid black;
                padding: 0.3em 0.5em;  
                text-align: left;
                vertical-align: top;
                line-height: 1.2;  
            }
            th {
                background-color: #f0f0f0;
                font-weight: bold;
                text-align: center;
            }
            td.number {
                text-align: right;
            }
            td.center {
                text-align: center;
            }
            .total-row {
                font-weight: bold;
                text-align: center;
            }
            .total-row td {
                font-weight: bold;
            }
            .total-row-text {
                font-weight: bold;
                text-align: right;
            }
            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
            }
            .signature-box {
                text-align: center;
                width: 45%;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .signature-content {
                margin-bottom: 2px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .signature-content img {
                height: 80px;
                max-height: 80px;
                width: auto;
            }
            .signature-space {
                height: 80px;  
            }
            .signature-line {
                border-top: 1px solid black;
                padding-top: 2px;
                margin-top: 2px;
                width: 100%;
            }
            .details-section {
                margin: 20px 0;
                page-break-inside: avoid;
            }
            .details-title {
                font-weight: bold;
                margin-top: 15px;
            }
            .details-content {
                margin-left: 20px;
            }
        </style>
    </head>
    <body>
        {% if header_logo %}
        <div class="header-logo">
            <img src="{{ header_logo }}" alt="Header Logo">
        </div>
        {% endif %}

        <div class="quotation-title">ใบเสนอราคา / Quotation</div>
        
        <div class="info-container">
            <div class="company-info">
                <div style="font-weight: bold;">{{ company_name }}</div>
                <div>{{ company_address }}</div>
                <div>โทร.: {{ company_tel }}</div>
                <div>เลขประจําตัวผู้เสียภาษี: {{ company_taxid }}</div>
            </div>
            <div class="quotation-info">
                <div>เลขที่ / No: {{ quotation_no }}</div>
                <div>วันที่ / Date: {{ formatted_date }}</div>
            </div>
        </div>
        
        <div class="customer-info">
            <div>ลูกค้า / Customer: {{ customer_name }}</div>
            <div>ที่อยู่ / Address: {{ customer_address }}</div>
            <div>โทร / Tel: {{ customer_tel }}</div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th style="width: 8%;">ลำดับ<br>No.</th>
                    <th style="width: 42%;">รายการ<br>Description</th>
                    <th style="width: 10%;">จำนวน<br>Quantity</th>
                    <th style="width: 7%;">หน่วย<br>Unit</th>
                    <th style="width: 18%;">ราคาต่อหน่วย<br>Unit Price</th>
                    <th style="width: 15%;">จำนวนเงิน<br>Amount</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td class="center">{{ loop.index }}</td>
                    <td>{{ item['description'] }}</td>
                    <td class="center">{{ item['qty'] }}</td>
                    <td class="center">{{ item['unit'] }}</td>
                    <td class="number">{{ "{:,.2f}".format(item['price']) }}</td>
                    <td class="number">{{ "{:,.2f}".format(item['total']) }}</td>
                </tr>
                {% endfor %}
                <tr class="total-row">
                    <td colspan="4" class="total-row-text">รวมเป็นเงิน / Total</td>
                    <td colspan="2"class="number">{{ "{:,.2f}".format(total_amount) }}<br>({{ total_amount_text }})</td>
                </tr>
            </tbody>
        </table>
        
        {% if start_date and end_date %}
        <div class="details-section">
            <div class="details-title">ระยะเวลาการทำงาน / Project Duration:</div>
            <div class="details-content">{{ format_date_thai_eng(start_date) }} - {{ format_date_thai_eng(end_date) }}</div>
        </div>
        {% endif %}

        {% if scope and scope.strip() %}
        <div class="details-section">
            <div class="details-title">ขอบเขตงาน / Scope of Work:</div>
            <div class="details-content">
                {% for line in scope.split('\n') %}
                {% if line.strip() %}
                <div>• {{ line.strip() }}</div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        {% if deliverables and deliverables.strip() %}
        <div class="details-section">
            <div class="details-title">ผลงานที่ส่งมอบ / Deliverables:</div>
            <div class="details-content">
                {% for line in deliverables.split('\n') %}
                {% if line.strip() %}
                <div>• {{ line.strip() }}</div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <div class="details-section">
            <div class="details-title">การชำระเงิน / Payment Terms:</div>
            <div class="details-content">{{ payment }}</div>
        </div>
        
        <div class="details-section">
            <div class="details-title">โอนเงินมาที่ / Bank Account:</div>
            <div class="details-content">{{ bank_account }}</div>
        </div>
        
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-space"></div>
                <div class="signature-line">ลงชื่อผู้สั่งซื้อ / Customer Signature</div>
                <div>วันที่ / Date: _________________</div>
            </div>
            <div class="signature-box">
                {% if signature_image %}
                <div class="signature-content">
                    <img src="{{ signature_image }}" alt="Signature" style="height: 80px;">
                </div>
                {% endif %}
                <div class="signature-line">ผู้มีอำนาจลงนาม / Authorized Signature</div>
                <div>วันที่ / Date: {{ current_date }}</div>
            </div>
        </div>

        {% if footer_logo %}
        <div class="footer-logo">
            <img src="{{ footer_logo }}" alt="Footer Logo">
        </div>
        {% endif %}
    </body>
    </html>
    """
    
    # Create template and render HTML
    template = Template(template_str)
    
    # Calculate total amount
    total = sum(float(item['price']) * float(item['qty']) for item in data.items)
    
    # Format current date
    current_date = format_date_thai_eng(data.date)  # Convert to Thai date format
    
    # Prepare items with calculated totals
    items_with_totals = []
    for item in data.items:
        items_with_totals.append({
            'description': item['description'],
            'qty': item['qty'],
            'unit': item.get('unit', 'ชิ้น'),  
            'price': float(item['price']),
            'total': float(item['price']) * float(item['qty'])
        })
    
    html_content = template.render(
        company_name=config.company_name,
        company_address=f"{config.address_1}, {config.address_2}",
        company_tel=config.tel,
        company_taxid=config.tax_id,
        quotation_no=data.quotation_no,
        date=data.date,
        formatted_date=format_date_thai_eng(data.date),
        customer_name=data.customer_name,
        customer_address=data.customer_address,
        customer_tel=data.customer_tel,
        items=items_with_totals,
        total_amount=total,
        total_amount_text=number_to_thai_text(total),
        scope=data.scope,
        deliverables=data.deliverables,
        payment=data.payment,
        bank_account=data.bank_account,
        start_date=data.start_date,
        end_date=data.end_date,
        header_logo=header_logo,
        footer_logo=footer_logo,
        signature_image=signature_image,
        current_date=current_date,
        format_date_thai_eng=format_date_thai_eng
    )
    
    # Generate PDF
    html = HTML(string=html_content)
    css = CSS(string="""
        @font-face {
            font-family: 'Sarabun';
            src: url('/usr/local/share/fonts/sarabun/Sarabun-Regular.ttf');
            font-weight: normal;
        }
        @font-face {
            font-family: 'Sarabun';
            src: url('/usr/local/share/fonts/sarabun/Sarabun-Bold.ttf');
            font-weight: bold;
        }
    """, font_config=font_config)
    
    # Generate the PDF
    buffer = BytesIO()
    html.write_pdf(buffer, stylesheets=[css], font_config=font_config)
    buffer.seek(0)
    return buffer.getvalue()

def generate_receipt_pdf(receipt: Receipt, config: CompanyConfig) -> bytes:
    """Generate a PDF receipt using WeasyPrint with proper Thai text support."""
    # Configure Fonts
    font_config = FontConfiguration()
    
    # Signature path
    signature_dict = {
        "warm": "assets/warm_sign.jpg",
        "may": "assets/may_sign.png",
    }

    # Convert image paths to data URLs
    def get_image_data_url(image_path):
        if not image_path:
            return None
        try:
            with open(image_path, 'rb') as img_file:
                encoded = base64.b64encode(img_file.read()).decode()
                mime_type = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'
                return f'data:{mime_type};base64,{encoded}'
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            return None

    # Get image data URLs
    header_logo = get_image_data_url(config.header_logo_path)
    footer_logo = get_image_data_url(config.footer_logo_path)

    # Format date
    formatted_date = format_date_thai_eng(receipt.date)
    current_date = format_date_thai_eng(str(date.today()))

    # Create HTML template
    template_str = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            @font-face {
                font-family: 'Sarabun';
                src: url('/usr/local/share/fonts/sarabun/Sarabun-Regular.ttf') format('truetype');
                font-weight: normal;
            }
            @font-face {
                font-family: 'Sarabun';
                src: url('/usr/local/share/fonts/sarabun/Sarabun-Bold.ttf') format('truetype');
                font-weight: bold;
            }
            @page {
                size: A4;
                margin: 0.3cm 2cm 0.3cm 2.5cm;
            }
            body {
                font-family: 'Sarabun', sans-serif;
                font-size: 14px;
                line-height: 1.3;
                margin: 0;
                padding-top: 0cm;
            }
            .header-logo {
                text-align: center;
                margin-top: 5px;
            }
            .header-logo img {
                height: 80px;
                max-width: 200px;
                object-fit: contain;
            }
            .footer-logo {
                text-align: center;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                margin-bottom: 5px;
            }
            .footer-logo img {
                height: 80px;
                max-width: 200px;
                object-fit: contain;
            }
            .receipt-title {
                font-size: 20px;
                font-weight: bold;
                text-align: right;
                margin: 20px 0;
            }
            .info-container {
                display: flex;
                justify-content: space-between;
                margin: 20px 0;
            }
            .company-info {
                flex: 1;
            }
            .receipt-info {
                text-align: right;
                margin-left: 20px;
            }
            .customer-info {
                margin: 20px 0;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 1em 0;
            }
            th, td {
                border: 1px solid black;
                padding: 0.3em 0.5em;  
                text-align: left;
                vertical-align: top;
                line-height: 1.2;  
            }
            th {
                background-color: #f0f0f0;
                font-weight: bold;
                text-align: center;
            }
            td.number {
                text-align: right;
            }
            td.center {
                text-align: center;
            }
            .total-row {
                font-weight: bold;
                text-align: center;
            }
            .total-row td {
                font-weight: bold;
            }
            .total-row-text {
                font-weight: bold;
                text-align: right;
            }
            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
            }
            .signature-box {
                text-align: center;
                width: 45%;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .signature-content {
                margin-bottom: 2px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .signature-content img {
                height: 80px;
                max-height: 80px;
                width: auto;
            }
            .signature-space {
                height: 80px;  
            }
            .signature-line {
                border-top: 1px solid black;
                padding-top: 2px;
                margin-top: 2px;
                width: 100%;
            }
            .thank-you {
                text-align: center;
                margin-top: 40px;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        {% if header_logo %}
        <div class="header-logo">
            <img src="{{ header_logo }}" alt="Header Logo">
        </div>
        {% endif %}
        
        <div class="receipt-title">
            ใบเสร็จรับเงิน / RECEIPT
        </div>
        
        <div class="info-container">
            <div class="company-info">
                <div style="font-weight: bold;">{{ config.company_name }}</div>
                <div>{{ config.address_1 }}</div>
                <div>{{ config.address_2 }}</div>
                <div>โทร.: {{ config.tel }}</div>
                <div>เลขประจําตัวผู้เสียภาษี: {{ config.tax_id }}</div>
            </div>
            <div class="receipt-info">
                <div>เลขที่ / No: {{ receipt.receipt_no }}</div>
                <div>วันที่ / Date: {{ formatted_date }}</div>
            </div>
        </div>
        
        <div class="customer-info">
            <div>ได้รับเงินจาก / Received from: {{ receipt.customer_name }}</div>
            <div>ที่อยู่ / Address: {{ receipt.customer_address }}</div>
            <div>โทร / Tel: {{ receipt.customer_tel }}</div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th style="width: 8%;">ลำดับ<br>No.</th>
                    <th style="width: 67%;">รายการ<br>Description</th>
                    <th style="width: 25%;">จำนวนเงิน<br>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="center">1</td>
                    <td>{{ receipt.description }}</td>
                    <td class="number">{{ "{:,.2f}".format(receipt.amount) }}</td>
                </tr>
                <tr class="total-row">
                    <td colspan="2" class="total-row-text">รวมเป็นเงิน / Total</td>
                    <td class="number">{{ "{:,.2f}".format(receipt.amount) }}<br>({{ receipt.amount_in_thai }})</td>
                </tr>
            </tbody>
        </table>
        
        <div class="signature-section">
            <div class="signature-box">
            </div>
            <div class="signature-box">
                {% if signature %}
                <div class="signature-content">
                    <img src="{{ signature }}" alt="Signature" style="height: 80px;">
                </div>
                {% endif %}
                <div class="signature-line">ผู้รับเงิน / Received by</div>
                <div>วันที่ / Date: {{ current_date }}</div>
            </div>
        </div>
        
        <div class="thank-you">
            ขอบคุณที่ใช้บริการ / Thank you for your business
        </div>
        
        {% if footer_logo %}
        <div class="footer-logo">
            <img src="{{ footer_logo }}" alt="Footer Logo">
        </div>
        {% endif %}
    </body>
    </html>
    """
    
    # Create Template and render HTML
    template = Template(template_str)
    html_content = template.render(
        receipt=receipt,
        config=config,
        header_logo=header_logo,
        footer_logo=footer_logo,
        signature=get_image_data_url(signature_dict[receipt.signature]),
        formatted_date=formatted_date,
        current_date=current_date
    )
    
    # Generate PDF
    html = HTML(string=html_content)
    css = CSS(string='', font_config=font_config)
    
    # Return PDF bytes
    pdf_buffer = BytesIO()
    html.write_pdf(pdf_buffer, stylesheets=[css], font_config=font_config)
    return pdf_buffer.getvalue()

def generate_invoice_pdf(invoice: Invoice, config: CompanyConfig) -> bytes:
    """Generate a PDF invoice using WeasyPrint with proper Thai text support."""
    # Configure fonts
    font_config = FontConfiguration()
    
    # Get current date in Thai-English format
    current_date = datetime.now().strftime("%Y-%m-%d")
    formatted_date = format_date_thai_eng(invoice.date)
    
    # Function to get image data URL
    def get_image_data_url(image_path):
        try:
            if not Path(image_path).exists():
                return None
            with open(image_path, 'rb') as img_file:
                encoded = base64.b64encode(img_file.read()).decode()
                mime_type = 'image/png' if image_path.lower().endswith('.png') else 'image/jpeg'
                return f'data:{mime_type};base64,{encoded}'
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            return None

    # Get image data URLs
    header_logo = get_image_data_url(config.header_logo_path)
    footer_logo = get_image_data_url(config.footer_logo_path)
    
    # Handle signature
    signature_dict = {
        'warm': 'assets/warm_sign.jpg',
        'may': 'assets/may_sign.png'
    }
    signature_image = None
    if invoice.signature and invoice.signature in signature_dict:
        signature_image = get_image_data_url(signature_dict[invoice.signature])
    
    # HTML template
    template_str = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            @font-face {
                font-family: 'Sarabun';
                src: url('/usr/local/share/fonts/sarabun/Sarabun-Regular.ttf') format('truetype');
                font-weight: normal;
            }
            @font-face {
                font-family: 'Sarabun';
                src: url('/usr/local/share/fonts/sarabun/Sarabun-Bold.ttf') format('truetype');
                font-weight: bold;
            }
            @page {
                size: A4;
                margin: 0.3cm 2cm 0.3cm 2.5cm;
            }
            body {
                font-family: 'Sarabun', sans-serif;
                font-size: 14px;
                line-height: 1.3;
                margin: 0;
                padding-top: 0cm;
            }
            .header-logo {
                text-align: center;
                margin-top: 5px;
            }
            .header-logo img {
                height: 80px;
                max-width: 200px;
                object-fit: contain;
            }
            .footer-logo {
                text-align: center;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                margin-bottom: 5px;
            }
            .footer-logo img {
                height: 80px;
                max-width: 200px;
                object-fit: contain;
            }
            .invoice-title {
                font-size: 20px;
                font-weight: bold;
                text-align: right;
                margin: 20px 0;
            }
            .info-container {
                display: flex;
                justify-content: space-between;
                margin: 20px 0;
            }
            .company-info {
                flex: 1;
            }
            .invoice-info {
                text-align: right;
                margin-left: 20px;
            }
            .customer-info {
                margin: 20px 0;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 1em 0;
            }
            th, td {
                border: 1px solid black;
                padding: 0.3em 0.5em;  
                text-align: left;
                vertical-align: top;
                line-height: 1.2;  
            }
            th {
                background-color: #f0f0f0;
                font-weight: bold;
                text-align: center;
            }
            td.number {
                text-align: right;
            }
            td.center {
                text-align: center;
            }
            .total-row {
                font-weight: bold;
            }
            .total-row td {
                font-weight: bold;
            }
            .total-row-text {
                font-weight: bold;
                text-align: right;
            }
            .bank-info {
                margin: 20px 0;
            }
            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
            }
            .signature-box {
                text-align: center;
                width: 45%;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .signature-content {
                margin-bottom: 2px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .signature-content img {
                height: 80px;
                max-height: 80px;
                width: auto;
            }
            .signature-space {
                height: 80px;  
            }
            .signature-line {
                border-top: 1px solid black;
                padding-top: 2px;
                margin-top: 2px;
                width: 100%;
            }
            .thank-you {
                text-align: center;
                margin-top: 40px;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        {% if header_logo %}
        <div class="header-logo">
            <img src="{{ header_logo }}" alt="Header Logo">
        </div>
        {% endif %}
        
        <div class="invoice-title">
            ใบแจ้งหนี้ / INVOICE
        </div>
        
        <div class="info-container">
            <div class="company-info">
                <div style="font-weight: bold;">{{ config.company_name }}</div>
                <div>{{ config.address_1 }}</div>
                <div>{{ config.address_2 }}</div>
                <div>โทร.: {{ config.tel }}</div>
                <div>เลขประจําตัวผู้เสียภาษี: {{ config.tax_id }}</div>
            </div>
            <div class="invoice-info">
                <div>เลขที่ / No: {{ invoice.invoice_no }}</div>
                <div>วันที่ / Date: {{ formatted_date }}</div>
            </div>
        </div>
        
        <div class="customer-info">
            <div>ชื่อ / Name: {{ invoice.customer_name }}</div>
            <div>ที่อยู่ / Address: {{ invoice.customer_address }}</div>
            <div>โทร / Tel: {{ invoice.customer_tel }}</div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th style="width: 8%;">ลำดับ<br>No.</th>
                    <th style="width: 67%;">รายการ<br>Description</th>
                    <th style="width: 25%;">จำนวนเงิน<br>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="center">1</td>
                    <td>{{ invoice.description }}</td>
                    <td class="number">{{ "{:,.2f}".format(invoice.amount) }}</td>
                </tr>
                <tr class="total-row">
                    <td colspan="2" class="total-row-text">รวมเป็นเงิน / Total</td>
                    <td class="number">{{ "{:,.2f}".format(invoice.amount) }}<br>({{ invoice.amount_in_thai }})</td>
                </tr>
            </tbody>
        </table>
        
        <div class="bank-info">
            <div>หมายเหตุ / Note:</div>
            <div>กรุณาชำระเงินภายใน 7 วัน / Please make payment within 7 days</div>
            <div>โอนเงินมาที่ / Bank Transfer to:</div>
            <div>{{ config.bank_account }}</div>
        </div>
        
        <div class="signature-section">
            <div class="signature-box">
            </div>
            <div class="signature-box">
                {% if signature_image %}
                <div class="signature-content">
                    <img src="{{ signature_image }}" alt="Signature" style="height: 80px;">
                </div>
                {% endif %}
                <div class="signature-line">ผู้ออกใบแจ้งหนี้ / Issued by</div>
                <div>วันที่ / Date: {{ current_date }}</div>
            </div>
        </div>
        
        <div class="thank-you">
            ขอบคุณที่ใช้บริการ / Thank you for your business
        </div>
        
        {% if footer_logo %}
        <div class="footer-logo">
            <img src="{{ footer_logo }}" alt="Footer Logo">
        </div>
        {% endif %}
    </body>
    </html>
    """
    
    # Create Template and render HTML
    template = Template(template_str)
    html_content = template.render(
        invoice=invoice,
        config=config,
        header_logo=header_logo,
        footer_logo=footer_logo,
        signature_image=signature_image,
        formatted_date=formatted_date,
        current_date=current_date
    )
    
    # Generate PDF
    html = HTML(string=html_content)
    css = CSS(string='', font_config=font_config)
    
    # Return PDF bytes
    pdf_buffer = BytesIO()
    html.write_pdf(pdf_buffer, stylesheets=[css], font_config=font_config)
    return pdf_buffer.getvalue()
