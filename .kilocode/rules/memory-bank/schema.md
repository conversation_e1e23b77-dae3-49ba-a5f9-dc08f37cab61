Quotation:
- document_no: str
- invoice_no: List[str] | none (fk: Invoice.document_no)
- receipt_no: List[str] | none (fk: Receipt.document_no)
- issue_date: date
- customer: Customer
- is_tax_inclusive: bool
- tax_rate: float | none
- item: List[Item]
- total: total value of all items = sum(item.qty * item.price) * (1+tax_rate)
- start_date: date | none
- end_date: date | none
- scope: str | none
- deliverables: str | none
- payment_terms: str | none
- note: str | none
- signature: enum | none
- company: CompanyConfig


Receipt:
- document_no: str
- quotation_no: str | none (fk: Quotation.document_no)
- invoice_no: str | none (fk: Invoice.document_no)
- issue_date: date
- customer: Customer
- is_tax_inclusive: bool
- tax_rate: float | none
- item: List[Item]
- total: total value of all items = sum(item.qty * item.price) * (1+tax_rate)
- note: str | none
- signature: enum | none
- company: CompanyConfig
- is_show_remaining_balance: bool


Invoice:
- document_no: str
- quotation_no: str | none (fk: Quotation.document_no)
- receipt_no: str | none (fk: Receipt.document_no)
- issue_date: date
- customer: Customer
- is_tax_inclusive: bool
- tax_rate: float | none
- item: List[Item]
- total: total value of all items = sum(item.qty * item.price) * (1+tax_rate)
- note: str | none
- signature: enum | none
- company: CompanyConfig
- is_show_remaining_balance: bool



Item:
    - description: str
    - qty: int
    - price: float

CompanyConfig:
    - name: str
    - address_1: str
    - address_2: str
    - tel: str
    - tax_id: str
    - bank_account: str
    - header_logo: str
    - footer_logo: str  

Customer:
    - name: str
    - address: str | none
    - tel: str | none
    - email: str | none
    - tax_id: str | none