I have an existing streamlit app that takes some information from the user and generates a pdf of quotation, invoice, and receipt. I would like to rework this project as a whole. 

I want to instead build a dedicated fastapi app to generate the pdf, that connect to another frontend outside of this project scope.

The improvements I want to make are:

1. make the data comes from request body instead of some from streamlit and some from config.yaml file  
2. rework the data model. I want to have it according to the schema.md file.
3. save and load function to a self-hosted teable database  (phase-2)
4. alembic-like migration system to handle database schema changes (phase-2)
5. deployment to gcp cloud run (phase-3)
